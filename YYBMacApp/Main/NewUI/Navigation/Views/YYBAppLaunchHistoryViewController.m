//
//  YYBAppLaunchHistoryViewController.m
//  YYBMacApp
//
//  Created by l<PERSON><PERSON><PERSON> on 2025/8/20.
//

#import "YYBAppLaunchHistoryViewController.h"
#import "YYBAppLaunchHistoryView.h"
#import "YYBAppLaunchHistory.h"
#import "YYBApkPackage.h"
#import "InstallApkInfo.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kTag = @"YYBAppLaunchHistoryViewController";

@interface YYBAppLaunchHistoryViewController ()

@property (nonatomic, strong) YYBAppLaunchHistoryView *historyView;

@end

@implementation YYBAppLaunchHistoryViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupHistoryView];
    [self setupNotifications];
    [self loadInitialData];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setupHistoryView {
    self.historyView = [[YYBAppLaunchHistoryView alloc] init];
    self.view = self.historyView;
}

- (void)setupNotifications {
    NSNotificationCenter *center = [NSNotificationCenter defaultCenter];
    
    // 监听APP打开通知
    [center addObserver:self
               selector:@selector(handleAppOpenedNotification:)
                   name:kYYBAppOpenedNotification
                 object:nil];
    
    // 监听APP卸载通知
    [center addObserver:self
               selector:@selector(handleAppUninstalledNotification:)
                   name:kYYBAppUninstalledNotification
                 object:nil];
}

- (void)loadInitialData {
    [self refreshHistoryDisplay];
}

- (void)refreshHistoryDisplay {
    // 获取最近的3个APP历史记录（从installedAppsMaps）
    NSArray<InstallApkInfo *> *recentApps = [[YYBApkPackage shared] getRecentAppsFromInstalledMaps:3];

    // 转换为YYBAppLaunchHistoryItem格式
    NSMutableArray<YYBAppLaunchHistoryItem *> *historyItems = [NSMutableArray array];
    for (InstallApkInfo *apkInfo in recentApps) {
        YYBAppLaunchHistoryItem *item = [YYBAppLaunchHistoryItem itemWithApkInfo:apkInfo];
        // 设置正确的启动时间（使用最后打开时间戳或安装时间戳）
        NSTimeInterval timestamp = apkInfo.lastOpenTimestamp > 0 ? apkInfo.lastOpenTimestamp : apkInfo.installTimestamp;
        if (timestamp > 0) {
            item.launchTime = [NSDate dateWithTimeIntervalSince1970:timestamp];
        }
        [historyItems addObject:item];
    }

    YYBMacLogInfo(kTag, @"刷新历史记录显示，获取到 %ld 个最近使用的APP", (long)historyItems.count);

    // 更新视图
    [self.historyView updateWithHistoryItems:historyItems];
}

#pragma mark - 通知处理

- (void)handleAppOpenedNotification:(NSNotification *)notification {
    InstallApkInfo *apkInfo = notification.userInfo[kYYBAppInfoKey];
    if (apkInfo) {
        YYBMacLogInfo(kTag, @"收到APP打开通知: %@", apkInfo.name);

        // 时间戳已经在openApp方法中更新，这里只需要刷新UI
        YYBMacLogInfo(kTag, @"[handleAppOpenedNotification] App opened: %@", apkInfo.name);

        // 刷新历史记录显示
        dispatch_async(dispatch_get_main_queue(), ^{
            [self refreshHistoryDisplay];
        });
    }
}

- (void)handleAppUninstalledNotification:(NSNotification *)notification {
    InstallApkInfo *apkInfo = notification.userInfo[kYYBAppInfoKey];
    if (apkInfo) {
        YYBMacLogInfo(kTag, @"收到APP卸载通知: %@", apkInfo.name);

        // 刷新历史记录显示
        dispatch_async(dispatch_get_main_queue(), ^{
            [self refreshHistoryDisplay];
        });
    }
}

@end
