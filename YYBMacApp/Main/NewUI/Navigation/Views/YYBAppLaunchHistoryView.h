//
//  YYBAppLaunchHistoryView.h
//  YYBMacApp
//
//  Created by lichenlin on 2025/8/20.
//

#import <Cocoa/Cocoa.h>

@class YYBAppLaunchHistoryItem;

NS_ASSUME_NONNULL_BEGIN

/**
 * APP打开历史记录视图
 * 支持展开和收起两种状态
 */
@interface YYBAppLaunchHistoryView : NSView

/**
 * 是否展开状态
 */
@property (nonatomic, assign, getter=isExpanded) BOOL expanded;

/**
 * 更新历史记录数据
 * @param historyItems 历史记录数组，最多显示3个
 */
- (void)updateWithHistoryItems:(NSArray<YYBAppLaunchHistoryItem *> *)historyItems;

@end

NS_ASSUME_NONNULL_END
