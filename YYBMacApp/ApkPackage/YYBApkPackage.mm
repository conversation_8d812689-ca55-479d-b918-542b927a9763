//
//  YYBApkPackage.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/5/30.
//

#import <Cocoa/Cocoa.h>
#import <CoreServices/CoreServices.h>
#import <pwd.h>
#import <sys/stat.h>
#import <unistd.h>
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "ApkInfoGenerater.h"
#import "ApkInstallTask.h"
#import "YYBApkPackage.h"
#import "YYBAppConfig.h"
#import "YYBDefine.h"
#import "YYBDefine.hpp"
#include "YYBMessageSender.h"
#import "YYBSocketDataSender.h"
#include "YYBSocketEngine.h"
#include "YYBSocketMsgData.h"
#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import "YYBAppLaunchHistory.h"

// 通知名称定义
NSString *const kYYBAppsChangedNotification = @"YYBAppsChangedNotification";
NSString *const kYYBAddedAppsKey = @"YYBAddedAppsKey";
NSString *const kYYBRemovedAppsKey = @"YYBRemovedAppsKey";

// APP打开和卸载通知
NSString *const kYYBAppOpenedNotification = @"YYBAppOpenedNotification";
NSString *const kYYBAppUninstalledNotification = @"YYBAppUninstalledNotification";
NSString *const kYYBAppInfoKey = @"YYBAppInfoKey";

static NSString *const kTag = @"ApkPackage";
void fetchEngineInstalledAppsCallback(const YYBSocketMsgData& msg) {
    if (msg.info.has_value()) {
    } else {
        YYBMacLogError(kTag, @"fetch Engine Installed Apps");
    }
}

@interface YYBApkPackage ()<ApkInstallTaskDelegate>
@property (nonatomic, assign) os_unfair_lock installedAppsLock;
@property (nonatomic, strong) NSMutableDictionary<NSString*, InstallApkInfo*> *installedAppsMaps;
@property (nonatomic, assign) os_unfair_lock taskLock;
@property (nonatomic, strong) NSMutableDictionary<NSString *, ApkInstallTask *> *installTasks;

// 按打开时间排序的APP包名列表（性能优化）
@property (nonatomic, strong) NSMutableArray<NSString *> *sortedInstalledAppPkgNames; // 按最后打开时间排序，最新的在前面

// 监听器相关
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSHashTable<id<YYBInstallUninstallListener>> *> *installUninstallListeners;
@property (nonatomic, strong) dispatch_queue_t listenerSyncQueue;

@end

@implementation YYBApkPackage
+ (instancetype)shared {
    static YYBApkPackage *instance = nil;
    static dispatch_once_t onceToken;

    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    if (self = [super init]) {
        _taskLock = OS_UNFAIR_LOCK_INIT;
        [self setupAppsDir];
        self.installTasks = [NSMutableDictionary dictionary];
        // 在不在引擎的都在 installedAppsMaps
        self.installedAppsMaps = [NSMutableDictionary dictionary];
        // 初始化按打开时间排序的APP列表
        self.sortedInstalledAppPkgNames = [NSMutableArray array];
        self.installUninstallListeners = [NSMutableDictionary dictionary];
        self.listenerSyncQueue = dispatch_queue_create("com.yyb.apk.listener.sync", DISPATCH_QUEUE_SERIAL);
        [self loadInstalledAppsFromDB];
    }

    return self;
}

// 统一设置状态和错误码描述，并自动分发状态通知
- (void)setInstallStatus:(InstallApkStatus)status
              forApkInfo:(InstallApkInfo *)apkInfo
               errorCode:(NSInteger)errorCode
            errorMessage:(NSString *)errorMsg
                  notify:(BOOL)notify {
    if (!apkInfo) return;
    apkInfo.installStatus = status;
    apkInfo.lastErrorCode = errorCode;
    apkInfo.lastErrorMessage = errorMsg;
    YYBMacLogInfo(kTag, @"[setInstallStatus] pkg:%@ status:%ld err:%ld msg:%@", apkInfo.pkgName, (long)status, (long)errorCode, errorMsg);

    // 如果是安装完成，将APP添加到排序列表的最前面
    if (status == InstallApkStatusInstallCompleted && apkInfo.isInstallInEngine) {
        [self moveAppToFrontInSortedList:apkInfo.pkgName];
    }

    if (notify) {
        [self notifyInstallStatusChanged:apkInfo];
    }
}

- (NSArray *)installedApps {
    os_unfair_lock_lock(&_installedAppsLock);
    // 过滤 是否在引擎中安装 -- 真正安装
    NSArray *filteredArray = [self.installedAppsMaps.allValues filteredArrayUsingPredicate:[NSPredicate predicateWithFormat:@"isInstallInEngine == YES"]];
    os_unfair_lock_unlock(&_installedAppsLock);

    return filteredArray;
}

- (void)setupAppsDir {
    // 快捷方式路径
    NSString *launchAppsDir = [ApkInfoGenerater launchAppsPath];

    NSError *error;
    NSFileManager *fm = [NSFileManager defaultManager];

    if (![fm createDirectoryAtPath:launchAppsDir withIntermediateDirectories:YES attributes:nil error:&error]) {
        YYBMacLogInfo(kTag, @"create YYBApplications error:%@", error);
    }

    // 默认App路径，不在启动台显示
    NSString *defaultAppsDir = [ApkInfoGenerater defaultAppPath];

    if (![fm createDirectoryAtPath:defaultAppsDir withIntermediateDirectories:YES attributes:nil error:&error]) {
        YYBMacLogInfo(kTag, @"create YYBApplications error:%@", error);
    }
}

/// Read from db
- (void)loadInstalledAppsFromDB {
    NSString *json = [[YYBMacMMKV sharedInstance] getStringForKey:@"YYBInstalledAppPackage"];
    NSData *jsonData = [json dataUsingEncoding:NSUTF8StringEncoding];
    if (!jsonData) {
        return;
    }
    
    NSError *err = nil;
    NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:jsonData
                                                       options:NSJSONReadingMutableContainers
                                                         error:&err];
    NSMutableDictionary *list = [NSMutableDictionary dictionaryWithDictionary:[self loadInstalledApps]];
    [dict enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
        InstallApkInfo *info = [InstallApkInfo yy_modelWithDictionary:obj];
        NSString *shotcutPath = [self appShotcutPathWithAppName:info.name];
        info.hasShotcut = shotcutPath != nil;
        list[key] = info;
    }];
    YYBMacLogInfo(kTag, @"loadInstalledAppsFromDB:%@", dict);
    os_unfair_lock_lock(&_installedAppsLock);
    self.installedAppsMaps = list;

    // 初始化排序列表（按最后打开时间排序）
    [self rebuildSortedAppList];

    os_unfair_lock_unlock(&_installedAppsLock);

}

- (void)saveToLocalDB {
    NSString *json = [self.installedAppsMaps yy_modelToJSONString];
    [[YYBMacMMKV sharedInstance] setString:json forKey:@"YYBInstalledAppPackage"];
}

/// 获取本地已安装APP（bundle）
- (NSDictionary *)loadInstalledApps {
    NSMutableDictionary<NSString *, InstallApkInfo *> *packageInfos = [NSMutableDictionary dictionary];
    NSFileManager *fm = [NSFileManager defaultManager];
    NSString *defaultAppPath = [ApkInfoGenerater defaultAppPath];
    NSString *launchAppsPath = [ApkInfoGenerater launchAppsPath];
    NSArray *defaultFiles = [fm contentsOfDirectoryAtPath:defaultAppPath error:nil];
    NSArray *launchFiles = [fm contentsOfDirectoryAtPath:launchAppsPath error:nil];
    NSSet *defaultSet = [NSSet setWithArray:defaultFiles];
    NSSet *launchSet = [NSSet setWithArray:launchFiles];

    NSMutableSet *onlyInDefault = [defaultSet mutableCopy];
    [onlyInDefault minusSet:launchSet];
    [packageInfos addEntriesFromDictionary:[self loadApps:onlyInDefault directory:defaultAppPath shotcut:NO]];

    [packageInfos addEntriesFromDictionary:[self loadApps:launchSet directory:launchAppsPath shotcut:YES]];

    return [packageInfos copy];
}

///
- (NSDictionary *)loadApps:(NSSet *)appBundles directory:(NSString *)directory shotcut:(BOOL)shotcut {
    NSMutableDictionary<NSString *, InstallApkInfo *> *packageInfos = [NSMutableDictionary dictionary];

    for (NSString *appBundleName in appBundles) {
        NSString *appPath = [directory stringByAppendingPathComponent:appBundleName];
        NSBundle *appBundle = [NSBundle bundleWithPath:appPath];

        if (appBundle) {
            InstallApkInfo *info = [[InstallApkInfo alloc] init];
            NSDictionary *dict = [appBundle infoDictionary];
            info.name = dict[@"CFBundleName"];
            info.pkgName = dict[@"YYBPackageName"] ? : @"";
            info.iconUrl = dict[@"YYBIconUrl"];
            info.hasShotcut = shotcut;
            packageInfos[info.pkgName] = info;
        }
    }

    return packageInfos;
}

#pragma mark - public
- (InstallApkInfo *)getApkInfo:(NSString *)pkgName {
    if (!pkgName) {
        return nil;
    }
    os_unfair_lock_lock(&_installedAppsLock);
    InstallApkInfo *pkgInfo = self.installedAppsMaps[pkgName];
    os_unfair_lock_unlock(&_installedAppsLock);
    return pkgInfo;
}

- (void)updateApkInfo:(InstallApkInfo *)apkInfo {
    os_unfair_lock_lock(&_installedAppsLock);
    self.installedAppsMaps[apkInfo.pkgName] = apkInfo;
    [self saveToLocalDB];
    os_unfair_lock_unlock(&_installedAppsLock);

}

- (BOOL)checkAppShotcut:(NSString *)pkgName {
    InstallApkInfo *pkgInfo = [self getApkInfo:pkgName];
    if (pkgInfo) {
        return false;
    }
    NSString *shotcutPath = [ApkInfoGenerater launchAppsPathFromName:pkgInfo.name];

    if (shotcutPath && [[NSFileManager defaultManager] fileExistsAtPath:shotcutPath]) {
        return true;
    }

    return false;
}

- (NSString *)appShotcutPathWithAppName:(NSString *)appName {
    NSString *shotcutPath = [ApkInfoGenerater launchAppsPathFromName:appName];

    if (shotcutPath && [[NSFileManager defaultManager] fileExistsAtPath:shotcutPath]) {
        return shotcutPath;
    }
    
    shotcutPath = [ApkInfoGenerater defaultAppPathForName:appName];

    if (shotcutPath && [[NSFileManager defaultManager] fileExistsAtPath:shotcutPath]) {
        return shotcutPath;
    }

    return nil;

}

/// 安装快捷方式
- (void)installShotcut:(NSString *)packageName completion:(_Nonnull InstallCompletionHandle)completion {
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        InstallApkInfo *apkInfo = [self getApkInfo:packageName];
        if (!apkInfo) {
            completion(nil, -1, @"APP not found");
            return;
        }

        ApkInstallTask *task = [[ApkInstallTask alloc] initWithApkInfo:apkInfo];
        os_unfair_lock_lock(&self->_taskLock);
        self.installTasks[apkInfo.pkgName] = task;
        os_unfair_lock_unlock(&self->_taskLock);
        task.delegate = self;
        [task addTaskCompletionCallback:completion];
        [task createShotcut];
    });
}

/// 安装Apk
- (void)installApp:(InstallApkInfo *)apkInfo
        completion:(_Nonnull InstallCompletionHandle)completion {
    [self installAppWithShotcut:apkInfo shouldCreateShotcut:YES completion:completion];
}

/// 安装Apk
- (void)installAppWithShotcut:(InstallApkInfo *)apkInfo
          shouldCreateShotcut:(BOOL)shouldCreateShotcut
                   completion:(_Nonnull InstallCompletionHandle)completion {
    if (apkInfo.pkgName.length == 0) {
        [self setInstallStatus:InstallApkStatusInstallError forApkInfo:apkInfo errorCode:ErrorParameterInvalid errorMessage:@"packageName is empty" notify:YES];
        YYBMacLogError(kTag, @"app info packageName is empty");
        completion(apkInfo, ErrorParameterInvalid, @"packageName is empty");
        return;
    }
    
    // 开始安装
    [self setInstallStatus:InstallApkStatusStartInstalling forApkInfo:apkInfo errorCode:0 errorMessage:nil notify:YES];

    // 设置安装时间戳（北京时间）
    apkInfo.installTimestamp = [InstallApkInfo currentBeijingTimestamp];
    
    InstallApkInfo *installedInfo = [self getApkInfo:apkInfo.pkgName];
    if (installedInfo) {
       ///TODO： 升级，更新合并数据
        apkInfo.isInstallInEngine = installedInfo.isInstallInEngine;
    }
    // 如果已安装，直接返回 TODO: 更新判断
    if (apkInfo.isInstallInEngine &&
        [[NSFileManager defaultManager] fileExistsAtPath:[ApkInfoGenerater defaultAppPathForName:apkInfo.name]]) {
        if (!shouldCreateShotcut ||
            [[NSFileManager defaultManager] fileExistsAtPath:[ApkInfoGenerater launchAppsPathFromName:apkInfo.name]]) {
            // 已完成
            [self setInstallStatus:InstallApkStatusInstallCompleted forApkInfo:apkInfo errorCode:0 errorMessage:nil notify:YES];
            completion(apkInfo, 0, nil);
            return;
        }
    }

    if (apkInfo.filePath.length > 0 && [[NSFileManager defaultManager] fileExistsAtPath:apkInfo.filePath]) {
        [self updateApkInfo:apkInfo];
        ApkInstallTask *task = nil;
        BOOL needInstall = NO;
        os_unfair_lock_lock(&_taskLock);
        task = self.installTasks[apkInfo.pkgName];

        if (!task || !task.isExecuting) {
            task = [[ApkInstallTask alloc] initWithApkInfo:apkInfo];
            task.delegate = self;
            self.installTasks[apkInfo.pkgName] = task;
            needInstall = YES;
        }
        
        os_unfair_lock_unlock(&_taskLock);
        [task addTaskCompletionCallback:^(InstallApkInfo * _Nullable completedApk, NSInteger retCode, NSString * _Nullable errorMessage) {
            if (retCode == 0) {
                [self setInstallStatus:InstallApkStatusInstallCompleted forApkInfo:completedApk errorCode:0 errorMessage:nil notify:YES];
            } else {
                [self setInstallStatus:InstallApkStatusInstallError forApkInfo:completedApk errorCode:retCode errorMessage:errorMessage notify:YES];
            }
            // 保证先业务回调，状态后分发
            if (completion) completion(completedApk, retCode, errorMessage);
        }];
        
        if (needInstall) {
            [self setInstallStatus:InstallApkStatusInstalling forApkInfo:apkInfo errorCode:0 errorMessage:nil notify:YES];
            [task installAppWithShotcut:shouldCreateShotcut];
        }
    } else {
        [self setInstallStatus:InstallApkStatusInstallError forApkInfo:apkInfo errorCode:ErrorParameterInvalid errorMessage:@"apk file is invalid" notify:YES];
        completion(apkInfo, ErrorParameterInvalid, @"apk file is invalid");
    }
}

/// 卸载Apk，默认 不 从“我的应用”删除
- (void)uninstallApp:(NSString *)pkgName completion:(InstallCompletionHandle)completion {
    // 默认给 isRemovedFromMyApp 参数传 NO
    [self uninstallApp:pkgName isRemovedFromMyApp:NO completion:completion];
}

/// 卸载Apk
- (void)uninstallApp:(NSString *)pkgName isRemovedFromMyApp:(BOOL)isRemovedFromMyApp completion:(_Nonnull InstallCompletionHandle)completion {
    if (pkgName.length == 0) {
        YYBMacLogError(@"", @"pkgName is empty");
        completion(nil, ErrorParameterInvalid, @"pkgName is empty");
        return;
    }
    // 关闭app
    [self closeApp:pkgName];
    
    InstallApkInfo *apkInfo = [self getApkInfo:pkgName];
    if (apkInfo) {
        [self setInstallStatus:InstallApkStatusStartUninstalling forApkInfo:apkInfo errorCode:0 errorMessage:nil notify:YES];
    }

    // 卸载app
    [SocketDataSender sendRequest:@"uninstallApp"
                             from:kProcessAppStore
                             with:@{ @"pkgName": pkgName }.mutableCopy
                               to:kProcessEngine
                         callback:^(NSDictionary *_Nullable response, NSError *_Nullable error) {
        InstallApkInfo *info = [self getApkInfo:pkgName];

        if (!info) {
            completion(nil, 0, @"");
            return;
        }

        BOOL isSuccess = !error;
        NSString *err = response[@"errorString"];
        if (isSuccess) {
            NSString *launchAppPath = [ApkInfoGenerater launchAppsPathFromName:info.name];
            NSString *defaultAppPath = [ApkInfoGenerater defaultAppPathForName:info.name];

            NSFileManager *fm = [NSFileManager defaultManager];
            NSError *err;
            if ([fm fileExistsAtPath:launchAppPath] && ![fm removeItemAtPath:launchAppPath
                                                                       error:&err]) {
                YYBMacLogError(kTag, @"removeItemAtPath failed: %@, err:%@", launchAppPath, err);
            }

            if ([fm fileExistsAtPath:defaultAppPath] && ![fm removeItemAtPath:defaultAppPath
                                                                       error:&err]) {
                YYBMacLogError(kTag, @"removeItemAtPath failed: %@, err:%@", defaultAppPath, err);
            }

            [self setInstallStatus:InstallApkStatusUninstallCompleted forApkInfo:info errorCode:0 errorMessage:nil notify:YES];

            // 发送APP卸载通知
            [[NSNotificationCenter defaultCenter] postNotificationName:kYYBAppUninstalledNotification
                                                                object:self
                                                              userInfo:@{kYYBAppInfoKey: info}];
        } else {
            [self setInstallStatus:InstallApkStatusUninstallError forApkInfo:info errorCode:error.code errorMessage:err notify:YES];
        }

        completion(info, error.code, err ? : error.localizedDescription);

        os_unfair_lock_lock(&self->_installedAppsLock);
    
        if (isRemovedFromMyApp) {
            // 完全卸载：从installedAppsMaps中删除
            [self.installedAppsMaps removeObjectForKey:pkgName];
            YYBMacLogInfo(kTag, @"完全卸载APP: %@，从installedAppsMaps中删除", pkgName);
        } else {
            // 历史安装：保留在installedAppsMaps中，但设置isInstallInEngine为NO
            if (info) {
                info.isInstallInEngine = NO;
                YYBMacLogInfo(kTag, @"APP卸载但保留历史: %@，isInstallInEngine设为NO", pkgName);
            }
        }
        [self saveToLocalDB];

        os_unfair_lock_unlock(&self->_installedAppsLock);

        // 从排序列表中移除（性能优化）
        [self removeAppFromSortedList:pkgName];
    }];
}

/// 卸载快捷方式
- (void)uninstallShotcut:(NSString *)pkgName completion:(_Nonnull InstallCompletionHandle)completion {
    InstallApkInfo *apkInfo = [self getApkInfo:pkgName];
    NSString *shotcutPath = [ApkInfoGenerater launchAppsPathFromName:apkInfo.name];

    if (shotcutPath && ![[NSFileManager defaultManager] fileExistsAtPath:shotcutPath]) {
        completion(apkInfo, 0, nil);
        return;
    }

    if (shotcutPath && [[NSFileManager defaultManager] removeItemAtPath:shotcutPath error:nil]) {
        completion(apkInfo, 0, nil);
        return;
    }

    completion(apkInfo, -1, @"uninstall failed");
}

- (void)openApp:(NSString *)pkgName {
    InstallApkInfo *info = [self getApkInfo:pkgName];

    if (!info) {
        YYBMacLogError(kTag, @"App is empty for package: %@", pkgName);
        return;
    }

    NSString *appPath = [self appShotcutPathWithAppName:info.name];
    if (!appPath) {
        YYBMacLogError(kTag, @"App path is empty for package: %@", pkgName);
        return;
    }
    NSURL *appUrl = [NSURL fileURLWithPath:appPath];

    NSWorkspaceOpenConfiguration *config = [NSWorkspaceOpenConfiguration configuration];
    config.activates = YES;

    // 更新打开时间戳（北京时间）
    info.lastOpenTimestamp = [InstallApkInfo currentBeijingTimestamp];
    [self updateApkInfo:info];

    // 更新排序列表（将此APP移到最前面）
    [self moveAppToFrontInSortedList:info.pkgName];

    // 发送APP打开通知
    [[NSNotificationCenter defaultCenter] postNotificationName:kYYBAppOpenedNotification
                                                        object:self
                                                      userInfo:@{kYYBAppInfoKey: info}];

    [[NSWorkspace sharedWorkspace] openApplicationAtURL:appUrl
                                          configuration:config
                                      completionHandler:^(NSRunningApplication *runningApp, NSError *error) {
        if (error) {
            YYBMacLogError(kTag, @"Failed to launch app: %@", error.localizedDescription);
        }
    }];
}

#pragma mark - package upgrade
- (void)upgradePackage {
    if (![YYBAppConfig isFirstLaunchOfCurrentVersion] && ![YYBAppConfig isDebug]) {
        return;
    }

    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        NSFileManager *fm = [NSFileManager defaultManager];
        NSString *mainBundlePath = [[NSBundle mainBundle] bundlePath];
        NSString *templateAppPath = [[NSBundle mainBundle] pathForResource:@"YYBPackage.app" ofType:nil];
        NSString *newerExePath = [templateAppPath stringByAppendingPathComponent:@"Contents/MacOS/YYBPackage"];
        NSString *infoPlistPath = [templateAppPath stringByAppendingPathComponent:@"Contents/Info.plist"];
        NSDictionary *infoPlist = [[NSDictionary alloc] initWithContentsOfFile:infoPlistPath];
        NSString *newerMd5 = infoPlist[@"md5"];
        NSDictionary *allInstalledApps = self.installedAppsMaps.copy;
        [allInstalledApps enumerateKeysAndObjectsUsingBlock:^(NSString * _Nonnull packageName, InstallApkInfo * _Nonnull info, BOOL * _Nonnull stop) {
            NSString *appPath = [self appShotcutPathWithAppName:info.name];
            if (!appPath) {
                return;
            }
            NSError *error = nil;
            NSBundle *appBundle = [NSBundle bundleWithURL:[NSURL fileURLWithPath:appPath]];
            NSMutableDictionary *appInfoList = [appBundle infoDictionary].mutableCopy;
            NSString *appMd5 = appInfoList[@"md5"];
            NSString *appModeDir = appInfoList[@"YYBModeDir"];
            NSString *exePath = [appPath stringByAppendingPathComponent:@"Contents/MacOS/YYBPackage"];

            BOOL shouldReCodesign = NO;
            if (![newerMd5 isEqualToString:appMd5] || ![appModeDir isEqualToString:mainBundlePath]) {
                YYBMacLogInfo(kTag, @"upgrade package:%@", info.name);
                shouldReCodesign = YES;
                error = nil;

                if ([fm fileExistsAtPath:exePath] && ![fm removeItemAtPath:exePath error:&error]) {
                    if (error) {
                        YYBMacLogInfo(kTag, @"refresh error:%@", error);
                    }
                }

                if (![fm copyItemAtPath:newerExePath toPath:exePath error:&error]) {
                    YYBMacLogInfo(kTag, @"refresh error:%@", error);
                }

                NSDictionary *newerInfoDict = [ApkInfoGenerater configAppInfoPlistDict:appInfoList pkgInfo:info];
                NSString *appInfoPath = [appPath stringByAppendingPathComponent:@"Contents/Info.plist"];

                if (![newerInfoDict writeToFile:appInfoPath atomically:YES]) {
                    YYBMacLogError(kTag, @"Failed to write Info.plist");
                }
            }

            if (shouldReCodesign) {
                [ApkInfoGenerater codesign:appPath];
                [ApkInfoGenerater removeQuarantineAttribute:appPath];
            }
        }];

        YYBMacLogInfo(kTag, @"reinstall finished");
    });
}

#pragma mark - Engine Callback
- (void)updateInstalledInEngineStatus:(NSArray *)installedAppList {
    os_unfair_lock_lock(&_installedAppsLock);
    NSMutableDictionary *newerInstalled = [NSMutableDictionary dictionaryWithDictionary:self.installedAppsMaps];
    
    NSMutableArray *addedApps = [NSMutableArray array];
    NSMutableArray *removedApps = [NSMutableArray array];
    NSMutableSet *engineInstalledPkgNames = [NSMutableSet set];
    for (NSDictionary *dict in installedAppList) {
        NSString *pkgName = dict[@"pkgName"];
        if (pkgName.length != 0) {
            [engineInstalledPkgNames addObject:pkgName];
            
            InstallApkInfo *apkInfo = newerInstalled[pkgName];
            if (apkInfo) {
                // 已存在的应用，更新状态
                if (!apkInfo.isInstallInEngine) {
                    apkInfo.isInstallInEngine = YES;
                    [self setInstallStatus:InstallApkStatusInstallCompleted forApkInfo:apkInfo errorCode:0 errorMessage:nil notify:YES];
                    [addedApps addObject:apkInfo];
                }
            } else {
                // 全新的应用
                InstallApkInfo *newApkInfo = [[InstallApkInfo alloc] init];
                newApkInfo.name = dict[@"appName"];
                newApkInfo.pkgName = pkgName;
                newApkInfo.isInstallInEngine = YES;
                // 设置安装时间戳
                newApkInfo.installTimestamp = [InstallApkInfo currentBeijingTimestamp];
                [self setInstallStatus:InstallApkStatusInstallCompleted forApkInfo:newApkInfo errorCode:0 errorMessage:nil notify:YES];
                newerInstalled[pkgName] = newApkInfo;
                [addedApps addObject:newApkInfo];
            }
        }
    }
    
    // 检查已删除的应用
    [newerInstalled enumerateKeysAndObjectsUsingBlock:^(NSString *pkgName, InstallApkInfo *apkInfo, BOOL *stop) {
        if (apkInfo.isInstallInEngine && ![engineInstalledPkgNames containsObject:pkgName]) {
            apkInfo.isInstallInEngine = NO;
            [self setInstallStatus:InstallApkStatusUninstallCompleted forApkInfo:apkInfo errorCode:0 errorMessage:nil notify:YES];
            [removedApps addObject:apkInfo];
        }
    }];
    
    if (addedApps.count > 0) {
        YYBMacLogInfo(kTag, @"新增应用数量: %lu", (unsigned long)addedApps.count);
    }
    
    if (removedApps.count > 0) {
        YYBMacLogInfo(kTag, @"删除应用数量: %lu", (unsigned long)removedApps.count);
    }
    
    if (addedApps.count > 0 || removedApps.count > 0) {
        NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
        if (addedApps.count > 0) {
            userInfo[kYYBAddedAppsKey] = addedApps;
        }
        if (removedApps.count > 0) {
            userInfo[kYYBRemovedAppsKey] = removedApps;
        }
        
        // 在主线程发送通知
        dispatch_async(dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] postNotificationName:kYYBAppsChangedNotification
                                                                object:self
                                                              userInfo:userInfo];
        });
    }
    
    self.installedAppsMaps = newerInstalled.mutableCopy;
    [self saveToLocalDB];
    os_unfair_lock_unlock(&_installedAppsLock);
}

- (void)updateEngineInstalledApp:(NSString *)pkgName {
    InstallApkInfo *info = [self getApkInfo:pkgName];

    if (info) {
        info.isInstallInEngine = YES;
        // 如果还没有安装时间戳，设置一个
        if (info.installTimestamp <= 0) {
            info.installTimestamp = [InstallApkInfo currentBeijingTimestamp];
        }
        // 当前引擎内安装app，理论上只有成功后才回调~
        [self setInstallStatus:InstallApkStatusInstallCompleted forApkInfo:info errorCode:0 errorMessage:nil notify:YES];
    } else {
        // TODO: 引擎内安装app，创建快捷方式打开
    }
    
}


- (void)updateEngineUninstallApp:(NSString *)pkgName {
    if (!pkgName) {
        return;
    }

    InstallApkInfo *info = [self getApkInfo:pkgName];
    info.isInstallInEngine = NO;
    
    [self setInstallStatus:InstallApkStatusUninstallCompleted forApkInfo:info errorCode:0 errorMessage:nil notify:YES];
}

#pragma mark - ApkInstallTaskDelegate
- (void)apkInstallTaskDidFinished:(nonnull InstallApkInfo *)apkInfo {
    os_unfair_lock_lock(&_taskLock);
    [self.installTasks removeObjectForKey:apkInfo.pkgName];
    [self saveToLocalDB];
    os_unfair_lock_unlock(&_taskLock);
}

#pragma mark - Debug
- (void)installLocalApp:(InstallApkInfo *)apkInfo completion:(_Nonnull InstallCompletionHandle)completion {
    if (!apkInfo.filePath) {
        return;
    }

    __weak typeof(self) weakSelf = self;
    NSImage *badge = [NSImage imageNamed:@"BadgeIcon"];
    [ApkInfoGenerater getApkInfoWithPath:apkInfo.filePath
                                   badge:badge
                              badgeFloat:0.25
                              completion:^(NSString *packageName, NSString *appName, NSString *iconPath) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        apkInfo.name = appName;
        apkInfo.pkgName = packageName;
        apkInfo.iconPath = iconPath;
        [strongSelf installApp:apkInfo
                    completion:completion];
        return;
    }];
}

- (void)installShotcutWithInfo:(InstallApkInfo *)apkInfo completion:(_Nonnull InstallCompletionHandle)completion {
    [self updateApkInfo:apkInfo];
    [self installShotcut:apkInfo.pkgName completion:completion];
}

- (void)closeApp:(NSString *)packageName {
    NSString *targetBundleID = [ApkInfoGenerater appBundleIdForPkgName:packageName];
    NSArray<NSRunningApplication *> *runningApps = [[NSWorkspace sharedWorkspace] runningApplications];
    for (NSRunningApplication *app in runningApps) {
        if ([app.bundleIdentifier isEqualToString:targetBundleID]) {
            [app terminate];
            break;
        }
    }
}

#pragma mark - Sorted App List Management (性能优化)

/// 重建排序列表（按最后打开时间排序）
- (void)rebuildSortedAppList {
    // 注意：此方法应该在已经持有 _installedAppsLock 的情况下调用

    NSMutableArray<InstallApkInfo *> *allApps = [NSMutableArray arrayWithArray:self.installedAppsMaps.allValues];

    // 按时间戳排序
    [allApps sortUsingComparator:^NSComparisonResult(InstallApkInfo *obj1, InstallApkInfo *obj2) {
        NSTimeInterval time1 = obj1.lastOpenTimestamp > 0 ? obj1.lastOpenTimestamp : obj1.installTimestamp;
        NSTimeInterval time2 = obj2.lastOpenTimestamp > 0 ? obj2.lastOpenTimestamp : obj2.installTimestamp;

        if (time1 > time2) {
            return NSOrderedAscending;  // 时间更新的排在前面
        } else if (time1 < time2) {
            return NSOrderedDescending;
        } else {
            return NSOrderedSame;
        }
    }];

    // 更新排序列表
    [self.sortedInstalledAppPkgNames removeAllObjects];
    for (InstallApkInfo *info in allApps) {
        [self.sortedInstalledAppPkgNames addObject:info.pkgName];
    }
}

/// 将APP移到排序列表的最前面（当APP被打开时调用）
- (void)moveAppToFrontInSortedList:(NSString *)pkgName {
    if (!pkgName.length) return;

    os_unfair_lock_lock(&_installedAppsLock);

    // 如果已存在，先移除
    [self.sortedInstalledAppPkgNames removeObject:pkgName];

    // 插入到最前面
    [self.sortedInstalledAppPkgNames insertObject:pkgName atIndex:0];

    os_unfair_lock_unlock(&_installedAppsLock);
}

/// 从排序列表中移除APP（当APP被卸载时调用）
- (void)removeAppFromSortedList:(NSString *)pkgName {
    if (!pkgName.length) return;

    os_unfair_lock_lock(&_installedAppsLock);
    [self.sortedInstalledAppPkgNames removeObject:pkgName];
    os_unfair_lock_unlock(&_installedAppsLock);
}

#pragma mark - APP打开历史

/// 获取按最后打开时间排序的APP列表（优化版本，基于预排序列表）
- (NSArray<InstallApkInfo *> *)getRecentAppsFromInstalledMaps:(NSInteger)maxCount {
    NSMutableArray<InstallApkInfo *> *result = [NSMutableArray array];

    os_unfair_lock_lock(&_installedAppsLock);

    // 直接从已排序的列表中获取APP（性能最优）
    for (NSString *pkgName in self.sortedInstalledAppPkgNames) {
        InstallApkInfo *info = self.installedAppsMaps[pkgName];
        if (info && info.isInstallInEngine) { // 只包含已安装的APP
            [result addObject:info];
            if (maxCount > 0 && result.count >= maxCount) {
                break;
            }
        }
    }

    os_unfair_lock_unlock(&_installedAppsLock);

    return [result copy];
}

- (NSArray *)getLaunchHistory:(NSInteger)maxCount {
    return [YYBAppLaunchHistory getLaunchHistory:maxCount];
}

- (NSArray *)getRecentApps:(NSInteger)maxCount {
    return [YYBAppLaunchHistory getRecentApps:maxCount];
}

- (void)clearHistoryForApp:(NSString *)pkgName {
    [YYBAppLaunchHistory clearHistoryForApp:pkgName];
}

- (void)clearAllHistory {
    [YYBAppLaunchHistory clearAllHistory];
}

#pragma mark - Listener 注册、移除相关API
- (void)addInstallUninstallListener:(id<YYBInstallUninstallListener>)listener {
    if (!listener) return;
    NSString *pkgName = [listener listenerPkgName];
    if (!pkgName.length) {
        YYBMacLogError(kTag, @"[addInstallUninstallListener] listener %@ pkgName为空，无法注册", listener);
        return;
    }
    dispatch_async(self.listenerSyncQueue, ^{
        NSHashTable *listeners = self.installUninstallListeners[pkgName];
        if (!listeners) {
            listeners = [NSHashTable weakObjectsHashTable];
            self.installUninstallListeners[pkgName] = listeners;
        }
        [listeners addObject:listener];
        YYBMacLogInfo(kTag, @"[addInstallUninstallListener] 注册listener: %@ 到 pkgName: %@", listener, pkgName);
    });
}

- (void)removeInstallUninstallListener:(id<YYBInstallUninstallListener>)listener {
    if (!listener) return;
    NSString *pkgName = [listener listenerPkgName];
    if (!pkgName.length) {
        YYBMacLogError(kTag, @"[removeInstallUninstallListener] listener %@ pkgName为空，无法移除", listener);
        return;
    }
    dispatch_async(self.listenerSyncQueue, ^{
        NSHashTable *listeners = self.installUninstallListeners[pkgName];
        [listeners removeObject:listener];
        if (listeners.count == 0) {
            [self.installUninstallListeners removeObjectForKey:pkgName];
        }
        YYBMacLogInfo(kTag, @"[removeInstallUninstallListener] 移除listener: %@ 从 pkgName: %@", listener, pkgName);
    });
}

- (nullable InstallApkInfo *)getInstallApkInfoForListener:(id<YYBInstallUninstallListener>)listener {
    if (!listener) return nil;
    NSString *pkgName = [listener listenerPkgName];
    if (!pkgName.length) {
        YYBMacLogError(kTag, @"[getInstallApkInfoForListener] listener %@ pkgName为空", listener);
        return nil;
    }
    return [self getApkInfo:pkgName];
}

- (nullable InstallApkInfo *)getInstallApkInfoForPkgName:(NSString *)pkgName {
    if (!pkgName.length) {
        YYBMacLogError(kTag, @"[getInstallApkInfoForPkgName] pkgName为空");
        return nil;
    }
    return [self getApkInfo:pkgName];
}

#pragma mark - Listener分发 — 状态变更通用派发
- (void)notifyInstallStatusChanged:(InstallApkInfo *)apkInfo {
    if (!apkInfo.pkgName.length) return;
    dispatch_async(self.listenerSyncQueue, ^{
        NSHashTable *listeners = self.installUninstallListeners[apkInfo.pkgName];
        NSArray *targets = listeners.allObjects.copy;
        if (!targets.count) {
            YYBMacLogInfo(kTag, @"[notifyInstallStatusChanged] 无监听者, pkgName=%@", apkInfo.pkgName);
            return;
        }
        for (id<YYBInstallUninstallListener> listener in targets) {
            if ([listener respondsToSelector:@selector(onInstallStatusChanged:)]) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    @try {
                        [listener onInstallStatusChanged:apkInfo];
                        YYBMacLogInfo(kTag, @"[notifyInstallStatusChanged] 分发InstallStatusChanged给listener:%@, pkgName:%@", listener, apkInfo.pkgName);
                    } @catch (NSException *e) {
                        YYBMacLogError(kTag, @"[notifyInstallStatusChanged] listener崩溃:%@, ex:%@", listener, e);
                    }
                });
            }
        }
    });
}

@end
