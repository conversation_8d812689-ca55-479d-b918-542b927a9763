//
//  YYBApkPackage.h
//  YYBMacApp
//
//  Created by <PERSON> on 2025/5/30.
//

#import <Foundation/Foundation.h>
#import "YYBApkPackageModel.h"
#import "ApkInstallTask.h"
#import "YYBInstallUninstallListener.h"

NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXPORT NSString *const kYYBAppsChangedNotification;
FOUNDATION_EXPORT NSString *const kYYBAddedAppsKey;
FOUNDATION_EXPORT NSString *const kYYBRemovedAppsKey;

// APP打开和卸载通知
FOUNDATION_EXPORT NSString *const kYYBAppOpenedNotification;
FOUNDATION_EXPORT NSString *const kYYBAppUninstalledNotification;
FOUNDATION_EXPORT NSString *const kYYBAppInfoKey;

@interface YYBApkPackage : NSObject

+ (instancetype)shared;
- (NSArray<InstallApkInfo *> *)installedApps; // 已安装列表

// 历史安装列表

/// 打开app
- (void)openApp:(NSString *)pkgName;

/// 检查是否已安装快捷方式
- (BOOL)checkAppShotcut:(NSString *)pkgName;

/// 安装快捷方式
- (void)installShotcut:(NSString *)packageName completion:(_Nonnull InstallCompletionHandle)completion;

/// 安装APK，默认创建快捷方式
- (void)installApp:(InstallApkInfo *)apkInfo completion:(_Nonnull InstallCompletionHandle)completion;

/// 安装Apk
/// @param shouldCreateShotcut 是否需要创建快捷方式
- (void)installAppWithShotcut:(InstallApkInfo *)apkInfo shouldCreateShotcut:(BOOL)shouldCreateShotcut completion:(_Nonnull InstallCompletionHandle)completion;

/// 卸载Apk
- (void)uninstallApp:(NSString *)pkgName isRemovedFromMyApp:(BOOL)isRemovedFromMyApp completion:(_Nonnull InstallCompletionHandle)completion;

/// 卸载快捷方式
- (void)uninstallShotcut:(NSString *)pkgName completion:(_Nonnull InstallCompletionHandle)completion;

/// 升级快捷方式
- (void)upgradePackage;

/// 引擎已安装列表
- (void)updateInstalledInEngineStatus:(NSArray *)installedAppList;

/// 引擎内安装成功回调，无论是从商店主动发起，还是引擎内发起安装都会有这个回调
- (void)updateEngineInstalledApp:(NSString *)pkgName;

/// 引擎内卸载成功回调，无论是从商店主动发起，还是引擎内发起卸载都会有这个回调
- (void)updateEngineUninstallApp:(NSString *)pkgName;

/// For Debug, 安装本地上传APK
- (void)installLocalApp:(InstallApkInfo *)apkInfo completion:(_Nonnull InstallCompletionHandle)completion;

- (void)installShotcutWithInfo:(InstallApkInfo *)apkInfo completion:(_Nonnull InstallCompletionHandle)completion;

#pragma mark - listener
// 注册/移除安装、卸载监听者（支持多对一监听，每个pkgName分组）
- (void)addInstallUninstallListener:(id<YYBInstallUninstallListener>)listener;
- (void)removeInstallUninstallListener:(id<YYBInstallUninstallListener>)listener;
// 按pkgName/Listener对象查询InstallApkInfo
- (nullable InstallApkInfo *)getInstallApkInfoForListener:(id<YYBInstallUninstallListener>)listener;
- (nullable InstallApkInfo *)getInstallApkInfoForPkgName:(NSString *)pkgName;

#pragma mark - Launch History
/// 获取App启动历史记录（按时间倒序）
/// @param maxCount 最大返回数量，0表示返回全部
- (NSArray *)getLaunchHistory:(NSInteger)maxCount;

/// 获取最近启动的App列表（去重，按最后启动时间倒序）
/// @param maxCount 最大返回数量，0表示返回全部
- (NSArray *)getRecentApps:(NSInteger)maxCount;

/// 清除指定App的历史记录
/// @param pkgName 包名
- (void)clearHistoryForApp:(NSString *)pkgName;

/// 清除所有历史记录
- (void)clearAllHistory;

@end

NS_ASSUME_NONNULL_END
